from nodeAPI import *
from node_lifterAPI import *
from enum import Enum

NODE_ID = 6
COB_ID1 = 0x180
COB_ID2 = 0x280
intefrace = 'vcan0'

# StartCanOpend()

class IternalSignals(Enum):
    lowSoc = 1 << 0
    highChargingCurrent = 1 << 1
    charging = 1 << 2
    allowCharging = 1 << 3
    chargingCurrentPresent = 1 << 4
    discharging = 1 << 5
    dischargingCurrentPresent = 1 << 6
    increasedVoltageEv = 1 << 7
    highDchTemperature = 1 << 8
    coolerOn = 1 << 9
    hygShutdown = 1 << 10
    init = 1 << 11
    precharging = 1 << 12
    combiiftShutdown = 1 << 13
    cellAnalysis = 1 << 14
    balancingSeries1 = 1 << 15
    balancingSeries2 = 1 << 16
    dischargingAux = 1 << 17
    acknowledgementPowerDown = 1 << 18
    crownEws = 1 << 19
    mainContactor = 1 << 20
    serviceReset = 1 << 21
    chargingDischarging = 1 << 22
    readyToCharge = 1 << 23
    readyToDischarge = 1 << 24
    powerUp = 1 << 25

class bms:
    def __init__(self):
        #Объекты словаря
        self.soc =  OD_OBJECT(NODE_ID, 0x4200, 0x07, 'u8')
        self.volt = OD_OBJECT(NODE_ID, 0x4200, 0x08, 'u16')
        self.curr = OD_OBJECT(NODE_ID, 0x4200, 0x02, 'u16')
        self.minCellVolt = OD_OBJECT(NODE_ID, 0x4200, 0x0B, 'u16')
        self.mosfets = OD_OBJECT(NODE_ID, 0x4050, 0x00, 'u8')
        self.iternalSignals = OD_OBJECT(NODE_ID, 0x4030, 0x00, 'u32')
        self.maxBoardTemp = OD_OBJECT(NODE_ID, 0x4200, 0x0D, 'i8')
        self.cellTemperatureMax = OD_OBJECT(NODE_ID, 0x4200, 0x06, 'i8')

        self.processor = CANMessageProcessor(intefrace)
        #Объекты маппинга 0x180
        self.pdoSoc = PDO_OBJECT(self.processor,  COB_ID1 + NODE_ID, 0, 1)
        self.pdoVolt = PDO_OBJECT(self.processor, COB_ID1 + NODE_ID, 1, 2)
        self.pdoCurr = PDO_OBJECT(self.processor, COB_ID1 + NODE_ID, 3, 2)
        self.pdoMinCellVolt = PDO_OBJECT(self.processor, COB_ID1 + NODE_ID, 5, 2)
        self.pdoMosfets = PDO_OBJECT(self.processor, COB_ID1 + NODE_ID, 7, 1)

        #Объекты маппинга 0x280
        self.pdoIternalSignals = PDO_OBJECT(self.processor,  COB_ID2 + NODE_ID, 0, 4)
        self.pdoMaxBoardTemp = PDO_OBJECT(self.processor,  COB_ID2 + NODE_ID, 4, 1)
        self.pdoCellTemperatureMax = PDO_OBJECT(self.processor,  COB_ID2 + NODE_ID, 5, 1)

    def startPDOProc(self):
        self.processor.start()

    def stopPDOProc(self):
        self.processor.stop()

    def startCanOpend(self):
        StartCanOpend()

    def getIternalSignalsPDO(self):
        activeSignals = []
        for signal in IternalSignals:
            if self.iternalSignals.value() & signal.value:
                activeSignals.append(signal.name)
        return activeSignals if activeSignals else ["No signal"]
    
    def getIternalSignalsSDO(self):
        activeSignals = []
        for signal in IternalSignals:
            if self.pdoIternalSignals.value() & signal.value:
                activeSignals.append(signal.name)
        return activeSignals if activeSignals else ["No signal"]

BMS = bms()

def checkValue(signal: int):
    try:
        return "NULL" if signal in (-1, None) else signal
    except:
        return "NULL"

if __name__ == "__main__":
    StartCanOpend()

