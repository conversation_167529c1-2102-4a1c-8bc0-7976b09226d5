from bmsAPI import *
from nodeAPI import *
import curses
import time

def main(stdscr):
    curses.curs_set(0)
    stdscr.nodelay(1)
    stdscr.timeout(10)
    
    BMS.startCanOpend()
    
    try:
        while True:
            soc = checkValue(BMS.pdoSoc.value())
            volt = checkValue(BMS.pdoVolt.value())
            curr = checkValue(BMS.pdoCurr.value())
            minCell = checkValue(BMS.pdoMinCellVolt.value())
            mosfets = checkValue(BMS.pdoMosfets.value())
            maxBoardTemp = checkValue(BMS.pdoMaxBoardTemp.value())
            cellTempMax = checkValue(BMS.pdoCellTemperatureMax.value())


            iternalSignals = BMS.getIternalSignalsPDO()
            iternalSignals = [] if iternalSignals in (-1, None) else iternalSignals
            
            stdscr.clear()
            
            stdscr.addstr(0, 0, f"BMS Monitoring (Ctrl+Z for exit)")
            stdscr.addstr(2, 0, f"Soc: {soc}")
            stdscr.addstr(3, 0, f"Voltage: {volt}")
            stdscr.addstr(4, 0, f"Current: {curr}")
            stdscr.addstr(5, 0, f"Min Cell Voltage: {minCell}")
            stdscr.addstr(6, 0, f"Mosfets: {mosfets}")
            stdscr.addstr(7, 0, f"Max Board Temperature: {maxBoardTemp}")
            stdscr.addstr(8, 0, f"Cell Temperature: {cellTempMax}")
            stdscr.addstr(9, 0, f"Iternal Signals: {iternalSignals}")

            stdscr.refresh()
                
            time.sleep(0.1)
    finally:
        curses.endwin()

if __name__ == "__main__":
    curses.wrapper(main)