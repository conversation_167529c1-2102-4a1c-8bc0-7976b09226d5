from bmsAPI import *
from nodeAPI import *
import curses
import time

def main(stdscr):
    curses.curs_set(0)
    stdscr.nodelay(1)
    stdscr.timeout(10)
    
    BMS.startPDOProc()
    
    try:
        while True:
            soc = checkValue(BMS.soc.value())
            volt = checkValue(BMS.volt.value())
            curr = checkValue(BMS.curr.value())
            minCell = checkValue(BMS.minCellVolt.value())
            mosfets = checkValue(BMS.mosfets.value())
            maxBoardTemp = checkValue(BMS.maxBoardTemp.value())
            cellTempMax = checkValue(BMS.cellTemperatureMax.value())

            iternalSignals = BMS.getIternalSignalsSDO()
            #iternalSignals = [] if iternalSignals in (-1, None) else iternalSignals

            stdscr.clear()
            
            stdscr.addstr(0, 0, f"Мониторинг BMS (Ctrl+Z для выхода)")
            stdscr.addstr(2, 0, f"Soc: {soc}")
            stdscr.addstr(3, 0, f"Voltage: {volt}")
            stdscr.addstr(4, 0, f"Current: {curr}")
            stdscr.addstr(5, 0, f"Min Cell Voltage: {minCell}")
            stdscr.addstr(6, 0, f"Mosfets: {mosfets}")
            stdscr.addstr(7, 0, f"Max Board Temperature: {maxBoardTemp}")
            stdscr.addstr(8, 0, f"Cell Temperature: {cellTempMax}")
            stdscr.addstr(9, 0, f"Iternal Signals: {iternalSignals}")

            stdscr.refresh()
                
            time.sleep(0.1)
    finally:
        BMS.stopPDOProc()
        curses.endwin()

if __name__ == "__main__":
    curses.wrapper(main)