import os
import can
import threading
import subprocess
import time
import logging

INTERFACE = 'vcan0'
CANOPEND_PATH = os.path.join(os.path.dirname(__file__), '/', 'bin', 'canopend')
COCOMM_PATH = os.path.join(os.path.dirname(__file__), '/', 'bin', 'cocomm')

def StartCan():
    os.system(f"sudo ip link set {INTERFACE} type can bitrate 500000")
    os.system(f"sudo ip link set {INTERFACE} up")

def StartCanOpend():
    try:
        if os.path.exists("/tmp/CO_command_socket"):
            os.remove("/tmp/CO_command_socket")
        process = subprocess.Popen([CANOPEND_PATH, INTERFACE, '-i', '55', '-c', 'local-/tmp/CO_command_socket'])
        logging.info("canopend успешно запущен в фоновом режиме.")
        return process  # Возвращаем объект процесса для дальнейшего управления
    except FileNotFoundError:
        logging.error(f"canopend не найден по пути: {CANOPEND_PATH}. Убедитесь, что он установлен.")
    except Exception as e:
        logging.error(f"Произошла ошибка: {e}")
        
def StartCanOpendHiden():
    with open(os.devnull, 'w') as devnull:
        try:
            if os.path.exists("/tmp/CO_command_socket"):
                os.remove("/tmp/CO_command_socket")
            process = subprocess.Popen([CANOPEND_PATH, INTERFACE, '-i', '55', '-c', 'local-/tmp/CO_command_socket'], 
                                       stdout=devnull, stderr=devnull)
            return process  # Возвращаем объект процесса для дальнейшего управления
        except FileNotFoundError:
            pass  # Игнорируем ошибку, если canopend не найден
        except Exception:
            pass  # Игнорируем все остальные ошибки

def shell_exec(command):
    try:
        result = subprocess.run(command, shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        output = result.stdout.decode('utf-8').strip()
        return output
    except subprocess.CalledProcessError as e:
        #print(f"Ошибка shell_exec: {e.stderr.decode('utf-8').strip()}\x1b[1A")
        return -1

class OD_OBJECT:
    def __init__(self, nodeId, index, subindex, data_type):
        self.nodeId = nodeId
        self.index = index
        self.subindex = subindex
        self.data_type = data_type

    def info(self):
        print(f"nodeId = {self.nodeId} | Index = {hex(self.index)} | subIndex = {hex(self.subindex)} | datatype = {self.data_type}")
    
    def setValue(self, value):
        return shell_exec(f'"{COCOMM_PATH}" "{self.nodeId} w {self.index} {self.subindex} {self.data_type} {value}"')
    
    def setValueArray(self, index, value):
        return shell_exec(f'"{COCOMM_PATH}" "{self.nodeId} w {self.index} {index} {self.data_type} {value}"')

    def value(self):
        return shell_exec(f'"{COCOMM_PATH}" "{self.nodeId} r {self.index} {self.subindex} {self.data_type}"')

    def __repr__(self):
        return shell_exec(f'"{COCOMM_PATH}" "{self.nodeId} r {self.index} {self.subindex} {self.data_type}"')
    
class OD_OBJECT_ARRAY(OD_OBJECT):
    def __init__(self, nodeId, index, data_type):
        super().__init__(nodeId, index, 0, data_type)
    
    def setValue(self, index, value):
        shell_exec(f'"{COCOMM_PATH}" "{self.nodeId} w {self.index} {index} {self.data_type} {value}"')

    def value(self, index):
        return shell_exec(f'"{COCOMM_PATH}" "{self.nodeId} r {self.index} {index} {self.data_type}"')

class CANMessageProcessor:
    def __init__(self, interface, target_ids=None, callback=None):
        self.interface = interface
        self.target_ids = target_ids if target_ids is not None else []
        self.callback = callback
        self.running = True
        self.thread = None
        self.pdo_objects = []
        self.received_ids = set()  # Хранит уникальные ID, от которых были сообщения

    def register_pdo_object(self, pdo_object):
        self.pdo_objects.append(pdo_object)

    def start(self):
        self.thread = threading.Thread(target=self._message_handler)
        self.thread.start()

    def stop(self):
        self.running = False
        if self.thread:
            self.thread.join()

    def _message_handler(self):
        with can.interface.Bus(channel=self.interface, bustype='socketcan') as bus:
            while self.running:
                message = bus.recv()
                if message is not None:
                    # Сохраняем ID сообщения
                    self.received_ids.add(message.arbitration_id)
                    
                    for pdo in self.pdo_objects:
                        if message.arbitration_id == pdo.message_id:
                            pdo.update(message)
                    if self.callback and message.arbitration_id in self.target_ids:
                        self.callback(message)

    def has_received_id(self, arbitration_id):
        return arbitration_id in self.received_ids


class PDO_OBJECT:
    def __init__(self, can_processor, message_id, start_index, length, data_processor=None):
        self.message_id = message_id
        self.start_index = start_index
        self.length = length
        self.data = None
        self.data_processor = data_processor  # Функция обработки данных
        can_processor.register_pdo_object(self)

    def update(self, message):
        raw_data = message.data[self.start_index:self.start_index + self.length]
        #print(f"Raw data from message: {raw_data}")  # Отладочный вывод
        self.data = int.from_bytes(raw_data, byteorder='little')
        if self.data_processor:
            self.data = self.data_processor(self.data)  # Применяем функцию обработки
        #self.info()


    def value(self):
        return self.data

    def info(self):
        print(f"Message ID = {hex(self.message_id)} | Start Index = {self.start_index} | Length = {self.length} | Data = {self.data}")

    def __repr__(self):
        return f"PDO_OBJECT(ID={hex(self.message_id)}, Data={self.data})"
