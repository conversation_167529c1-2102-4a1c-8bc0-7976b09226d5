from nodeAPI import *
from enum import Enum
import time

def get_first_three_bits(n):
    # Маска для извлечения трёх младших битов (111 в двоичном формате)
    mask = 0b111
    # Извлечение трёх младших битов с помощью операции побитового И
    least_significant_bits = n & mask
    return least_significant_bits

class LinearApprox:
    def __init__(self, x1, y1, x2, y2):
        self.k = (y2 - y1) / (x2 - x1)
        self.b = y2 - self.k * x2

    def calc_y(self, x):
        return self.k * x + self.b

def adc_to_current(adc_value, current_sensor_mv_a=540, current_lifter_max_current=4000):
    # Параметры
    max_adc_value = 4095  # Максимальное значение АЦП (12 бит)
    adc_ref_voltage = 3200  # Референсное напряжение АЦП в мВ

    # x1 и y1
    x1 = 0
    y1 = 0

    # x2 и y2 (из расчета аналогично вашему C-коду)
    x2 = int(((current_sensor_mv_a * current_lifter_max_current) / 1000.0) * max_adc_value / adc_ref_voltage)
    y2 = current_lifter_max_current

    # Создаем объект линейной аппроксимации
    linear_approx = LinearApprox(x1, y1, x2, y2)

    # Преобразуем значение АЦП в ток
    current = linear_approx.calc_y(adc_value)
    return max(0, int(current))  # Ограничение на положительное значение тока и приведение к целому числу

LIFTER_ID = 13

class Mode(Enum):
    NORMAL = 0
    EMERGENCY = 1
    ENDSTOP = 2
    ENCODER = 3

class Status(Enum):
    INIT = 7
    UP = 2
    DOWN = 1
    MOVE_UP = 3
    MOVE_DOWN = 4
    STOPPED = 5
    ERROR = 0
    USTIROVKA = 6

class lifter:
    def __init__(self):
        self.setPosition = OD_OBJECT(LIFTER_ID, 0x6402, 0x00, 'u8')
        self.mode = OD_OBJECT(LIFTER_ID, 0x6405, 0x00, 'u8')
        self.status = OD_OBJECT(LIFTER_ID, 0x6404, 0x00, 'u32')
        self.pdoTimeout = OD_OBJECT(LIFTER_ID, 0x1801, 0x05, 'u16')
        self.current = OD_OBJECT_ARRAY(LIFTER_ID, 0x640A, 'u32')
        self.adc = OD_OBJECT_ARRAY(LIFTER_ID, 0x6414, 'u16')
        self.encodersResetCommand = OD_OBJECT(LIFTER_ID, 0x641A, 0x01, 'u8')
        self.encoderStartAdjustment = OD_OBJECT(LIFTER_ID, 0x641A, 0x02, 'u8')
        self.encoderDownPos = OD_OBJECT(LIFTER_ID, 0x641B, 0x02, 'u16')
        self.weight = OD_OBJECT(LIFTER_ID, 0x6406, 0x00, 'u16')
        self.resetWarnings = OD_OBJECT(LIFTER_ID, 0x6407, 0x00, 'u8')
        self.resetError = OD_OBJECT(LIFTER_ID, 0x6408, 0x00, 'u8')

        # Создание экземпляра CANMessageProcessor и регистрация PDO
        self.can_processor = CANMessageProcessor(INTERFACE)
        #self.pdoStatus = PDO_OBJECT(self.can_processor, 0x18D, 0, 4, get_first_three_bits)
        self.pdoWeight = PDO_OBJECT(self.can_processor, 0x18D, 4, 2)
        self.pdoADC1 = PDO_OBJECT(self.can_processor, 0x28D, 0, 2)
        self.pdoADC2 = PDO_OBJECT(self.can_processor, 0x28D, 2, 2)
        self.pdoADC3 = PDO_OBJECT(self.can_processor, 0x28D, 4, 2)
        self.pdoADC4 = PDO_OBJECT(self.can_processor, 0x28D, 6, 2)
        
    def startPDOProc(self):
        self.can_processor.start()

    def stopPDOProc(self):
        self.can_processor.stop()

    def UP(self):
        self.setPosition.setValue(1)

    def DOWN(self):
        self.setPosition.setValue(2)

    def OFF(self):
        self.setPosition.setValue(0)

    def SetMode(self, mode):
        if isinstance(mode, Mode):
            self.mode.setValue(mode.value)
        elif isinstance(mode, int):
            self.mode.setValue(mode)
        else:
            raise ValueError("Режим должен быть либо экземпляром Mode, либо целым числом.")

    def GetMode(self):
        return int(self.mode.value())

    def GetModeStr(self):
        mode_int = int(self.mode.value())
        try:
            return Mode(mode_int).name
        except ValueError:
            #return f"Неизвестный режим: {mode_int}"
            return -1

    def GetStatus(self):
        status_int = int(self.status.value())
        return str(get_first_three_bits(status_int))
    
    def GetStatusStr(self):
        status_int = int(self.status.value())
        status_bits = get_first_three_bits(status_int)
        try:
            return Status(status_bits).name
        except ValueError:
            #return f"Неизвестный статус: {status_bits}"
            return -1

    def GetCurrent(self, channel):
        return self.current.value(channel)

    def GetCurrentFiltred(self, channel):
        return self.current.value(channel + 4)

    def GetCurrentPDO(self, channel):
        if channel == 1:
            value = self.pdoADC1.value()
        elif channel == 2:
            value = self.pdoADC2.value()
        elif channel == 3:
            value = self.pdoADC3.value()
        elif channel == 4:
            value = self.pdoADC4.value()
        
        if value is None:
            return 0  # Или другое значение по умолчанию
        
        return adc_to_current(int(value))


    def EncodersResetCommand(self):
        self.encodersResetCommand.setValue(1)

    def EncodersStartAdjustment(self):
        self.encoderStartAdjustment.setValue(1)

Lifter = lifter()

if __name__ == "__main__":
    StartCanOpend()  # Запуск canopend
    Lifter.pdoTimeout.setValue(1)
    print(Lifter.GetStatus())
    print(Lifter.GetStatusStr())

